/**
 * @description This class is responsible for retrieving all data related to cashflow for a specific project.
 * It serves as a data service for the Cashflow Tracker UI, ensuring Field Level Security (FLS)
 * and sharing rules are respected. It dynamically fetches fields for Project__c, Cashflow__c, Cashflow_Line_Item__c,
 * and its child object Cashflow_Line_Item_Child__c.
 * <AUTHOR>
 * @date 12 May 2025 (updated 27 May 2025 for child object integration)
 */
@SuppressWarnings('PMD.AvoidDeeplyNestedIfStmts, PMD.CognitiveComplexity, PMD.ExcessiveMethodLength,PMD')
public with sharing class CashflowDataService {
    
    private static final String CLASS_NAME = 'CashflowDataService';
    private static final String CHILD_LINE_ITEM_CHILD_OBJECT_API_NAME = 'Cashflow_Line_Item_Child__c';
    private static final String CHILD_LINE_ITEM_RELATIONSHIP_NAME = 'Cashflow_Line_Item_Child__r';
    private static final String DISBURSEMENT_JUNCTION_OBJECT_API_NAME = 'Cashflow_Weekly_Line_Disbursement__c';
    private static final String DISBURSEMENT_JUNCTION_RELATIONSHIP_NAME = 'Cashflow_Weekly_Line_Disbursements__r';
    private static final String DISBURSEMENT_OBJECT_API_NAME = 'Disbursement__c';
    // private static final String Pay_Application_JUNCTION_OBJECT_API_NAME = 'Cashflow_Weekly_Line_Pay_Application__c';
    // private static final String Pay_Application_JUNCTION_RELATIONSHIP_NAME = 'Cashflow_Weekly_Line_Pay_Application__r';
    // private static final String Pay_Application_OBJECT_API_NAME = 'Pay_Application__c';
    
    /**
     * @description Retrieves all accessible fields for a given SObject type.
     * @param sObjectApiName The API name of the SObject.
     * @return List<String> A list of API names of accessible fields.
     */
    private static List<String> getAllAccessibleFields(String sObjectApiName) {
        final String METHOD_NAME = 'getAllAccessibleFields';
        DebugLogUtil.entry(CLASS_NAME + '.' + METHOD_NAME, new Map<String, Object>{'sObjectApiName' => sObjectApiName});
        
        List<String> accessibleFields = new List<String>();
        if (String.isBlank(sObjectApiName)) {
            DebugLogUtil.warn(METHOD_NAME + ': SObject API name is blank.');
            return accessibleFields;
        }
        
        try {
            Map<String, Schema.SObjectType> globalDescribe = Schema.getGlobalDescribe();
            Schema.SObjectType sObjectType = globalDescribe.get(sObjectApiName.toLowerCase()); // Use toLowerCase for safety
            
            if (sObjectType == null) {
                DebugLogUtil.error(METHOD_NAME + ': SObject type not found for API name: ' + sObjectApiName);
                return accessibleFields;
            }
            
            Schema.DescribeSObjectResult sObjectDescribe = sObjectType.getDescribe();
            Map<String, Schema.SObjectField> fieldMap = sObjectDescribe.fields.getMap();
            
            for (Schema.SObjectField field : fieldMap.values()) {
                Schema.DescribeFieldResult fieldDescribe = field.getDescribe();
                if (fieldDescribe.isAccessible()) { // Check FLS for readability
                    accessibleFields.add(fieldDescribe.getName());
                }
            }
            DebugLogUtil.info(METHOD_NAME + ': Found ' + accessibleFields.size() + ' accessible fields for ' + sObjectApiName);
        } catch (Exception e) {
            DebugLogUtil.error(METHOD_NAME + ': Error getting fields for ' + sObjectApiName, e);
        }
        
        DebugLogUtil.exit(CLASS_NAME + '.' + METHOD_NAME);
        return accessibleFields;
    }
    
    /**
     * @description Retrieves all necessary data for the Cashflow Tracker UI for a given Project.
     * Applies FLS checks before returning data. Respects Sharing rules.
     * Fetches Project, Cashflow, parent Cashflow_Line_Item__c (with aggregate values),
     * and their child Cashflow_Line_Item_Child__c records.
     * @param projectId The Id of the Project__c record.
     * @param cashflowId The Id of the Cashflow__c record.
     * @return CashflowPageData Wrapper containing all permitted data for the UI.
     */
    @AuraEnabled(cacheable=true)
    public static CashflowPageData getCashflowData(Id projectId, Id cashflowId) {
        final String METHOD_NAME = 'getCashflowData';
        DebugLogUtil.entry(CLASS_NAME + '.' + METHOD_NAME, new Map<String, Object>{'projectId' => projectId, 'cashflowId' => cashflowId});
        
        CashflowPageData pageData = new CashflowPageData();
        
        if (String.isBlank(projectId)) {
            DebugLogUtil.error(METHOD_NAME + ': Project ID is null or blank.');
            throw new AuraHandledException('Project ID is required.');
        }
        
        try {
            // --- 1. Get Project Header Data ---
            List<String> projectFields = getAllAccessibleFields('Project__c');
            if (projectFields.isEmpty()) {
                DebugLogUtil.error(METHOD_NAME + ': No accessible fields found for Project__c. Cannot query.');
                throw new AuraHandledException('No accessible fields found for Project object.');
            }
            if(!projectFields.contains('Id')) {projectFields.add('Id');}
            
            String projectQuery = 'SELECT ' + String.join(projectFields, ',') +
                ' FROM Project__c WHERE Id = :projectId LIMIT 1';
            DebugLogUtil.info(METHOD_NAME + ': Dynamic Project Query: ' + projectQuery);
            List<SObject> tempProjectList = Database.query(projectQuery);
            if (!tempProjectList.isEmpty()) {
                SObjectAccessDecision projectDecision = Security.stripInaccessible(AccessType.READABLE, tempProjectList);
                if(!projectDecision.getRecords().isEmpty()){
                    pageData.project = (Project__c)projectDecision.getRecords()[0];
                    DebugLogUtil.info(METHOD_NAME + ': Project data fetched and FLS checked for Project ID: ' + projectId);
                } else { 
                    DebugLogUtil.warn(METHOD_NAME + ': Project found but no fields are accessible after FLS check for Project ID: ' + projectId);
                    throw new AuraHandledException('You do not have access to view the details of this project.');
                }
            } else { 
                DebugLogUtil.warn(METHOD_NAME + ': Project not found for ID: ' + projectId);
                throw new AuraHandledException('Project not found with ID: ' + projectId);
            }
               
            
            if(projectId != null){
                if(pageData.project.Account_Name__c!= null){
                    List<Account> acc = [Select Id, Name from Account where Id = :pageData.project.Account_Name__c LIMIT 1];
                    pageData.account = acc[0];
                }
                
                
            }



            List<Cashflow__c> siblings = [
                SELECT Id, Version_Number__c
                FROM Cashflow__c
                WHERE Project__c = :projectId
                AND Version_Number__c != null
                ORDER BY Version_Number__c
            ];
            // enforce FLS
            SObjectAccessDecision versionsDecision = Security.stripInaccessible(
                AccessType.READABLE,
                siblings
            );
            pageData.allCashflows = (List<Cashflow__c>) versionsDecision.getRecords();
            
            // --- 2. Get Active Cashflow Version and its Line Items (Parent and Children) ---
            Id activeCashflowIdToQuery;
            if(cashflowId != null) {
                activeCashflowIdToQuery = cashflowId;
            } else {
                activeCashflowIdToQuery = getActiveCashflowId(projectId);
            }
            
            if (activeCashflowIdToQuery != null) {
                DebugLogUtil.info(METHOD_NAME + ': Effective Cashflow ID for query: ' + activeCashflowIdToQuery + ' for Project ID: ' + projectId);
                
                List<String> cashflowFields = getAllAccessibleFields('Cashflow__c');
                if (cashflowFields.isEmpty()) { // ... (error handling) ...
                    DebugLogUtil.error(METHOD_NAME + ': No accessible fields found for Cashflow__c. Cannot query active cashflow.');
                    throw new AuraHandledException('No accessible fields found for Cashflow object.');
                }
                if(!cashflowFields.contains('Id')) {cashflowFields.add('Id');}
                // ... (active cashflow fetching remains similar) ...
                String activeCashflowQuery = 'SELECT ' + String.join(cashflowFields, ',') +
                    ' FROM Cashflow__c WHERE Id = :activeCashflowIdToQuery LIMIT 1';
                DebugLogUtil.info(METHOD_NAME + ': Dynamic Active Cashflow Query: ' + activeCashflowQuery);
                List<SObject> tempActiveCashflowList = Database.query(activeCashflowQuery);
                
                if(!tempActiveCashflowList.isEmpty()){
                    SObjectAccessDecision activeCashflowDecision = Security.stripInaccessible(AccessType.READABLE, tempActiveCashflowList);
                    if(!activeCashflowDecision.getRecords().isEmpty()){
                        pageData.activeCashflow = (Cashflow__c)activeCashflowDecision.getRecords()[0];
                        DebugLogUtil.info(METHOD_NAME + ': Active Cashflow record data fetched and FLS checked.');
                    } else { // ... (warning) ...
                        DebugLogUtil.warn(METHOD_NAME + ': Active Cashflow record found but no fields accessible after FLS for ID: ' + activeCashflowIdToQuery);
                    }
                } else { // ... (warning) ...
                    DebugLogUtil.warn(METHOD_NAME + ': Active Cashflow record not found for ID: ' + activeCashflowIdToQuery);
                }
                
                // // Fetch Parent Cashflow_Line_Item__c and their Children (Cashflow_Line_Item_Child__c)
                // List<String> forecastParentFields = getAllAccessibleFields('Cashflow_Line_Item__c');
                // if (forecastParentFields.isEmpty()) {
                //     DebugLogUtil.warn(METHOD_NAME + ': No accessible fields found for Cashflow_Line_Item__c. Forecast lines will be empty.');
                //     pageData.forecastLines = new List<Cashflow_Line_Item__c>();
                // } else {
                //     if(!forecastParentFields.contains('Id')) {forecastParentFields.add('Id');}
                //     if(!forecastParentFields.contains('Cashflow__c')) {forecastParentFields.add('Cashflow__c');}
                //     if(!forecastParentFields.contains('Week_Start_Date__c')) {forecastParentFields.add('Week_Start_Date__c');}
                //     if(!forecastParentFields.contains('Line_Item_Category__c')) {forecastParentFields.add('Line_Item_Category__c');}
                //     if(!forecastParentFields.contains('Planned_Amount__c')) {forecastParentFields.add('Planned_Amount__c');} // Crucial aggregate field
                //     // Add other parent CLI fields that might be used as templates for children if needed
                    
                //     String childSubQuery = '';
                //     if (Schema.getGlobalDescribe().containsKey(CHILD_LINE_ITEM_OBJECT_API_NAME.toLowerCase())) {
                //         List<String> childDetailAccessibleFields = getAllAccessibleFields(CHILD_LINE_ITEM_OBJECT_API_NAME);
                //         List<String> childFieldsToQuery = new List<String>{'Id'}; // Always include Id
                //         if(childDetailAccessibleFields.contains('Amount__c')) {childFieldsToQuery.add('Amount__c');}
                //         if(childDetailAccessibleFields.contains('Description__c')) {childFieldsToQuery.add('Description__c');}
                //         // Add other relevant child fields based on accessibility and popover needs
                //         // e.g., Sub_Amount_Variation__c, Sub_Payment_Frequency__c if they moved to child
                        
                //         if (childFieldsToQuery.size() > 0) {
                //             // Ensure the relationship name is correct
                //             childSubQuery = ', (SELECT ' + String.join(childFieldsToQuery, ',') +
                //                 ' FROM ' + CHILD_LINE_ITEM_RELATIONSHIP_NAME + ')';
                //         }
                //     } else {
                //         DebugLogUtil.warn(METHOD_NAME + ': Child object ' + CHILD_LINE_ITEM_OBJECT_API_NAME + ' not found in schema. Cannot query child line items.');
                //     }
                    
                //     String forecastQuery = 'SELECT ' + String.join(forecastParentFields, ',') + childSubQuery +
                //         ' FROM Cashflow_Line_Item__c WHERE Cashflow__c = :activeCashflowIdToQuery ' +
                //         ' ORDER BY Week_Start_Date__c NULLS FIRST, Line_Item_Category__c';
                //     DebugLogUtil.info(METHOD_NAME + ': Dynamic Forecast Lines Query (with children): ' + forecastQuery);
                    
                //     List<SObject> tempForecastLines = Database.query(forecastQuery);
                //     SObjectAccessDecision forecastDecision = Security.stripInaccessible(AccessType.READABLE, tempForecastLines);
                //     pageData.forecastLines = (List<Cashflow_Line_Item__c>)forecastDecision.getRecords();
                //     DebugLogUtil.info(METHOD_NAME + ': ' + pageData.forecastLines.size() + ' parent forecast lines (with children) fetched and FLS checked for Active Cashflow ID: ' + activeCashflowIdToQuery);
                // }
                
                // Fetch Parent Cashflow_Line_Item__c and their Children (Cashflow_Line_Item_Child__c)
                List<String> forecastParentFields = getAllAccessibleFields('Cashflow_Line_Item__c');
                if (forecastParentFields.isEmpty()) {
                    DebugLogUtil.warn(METHOD_NAME + ': No accessible fields for Cashflow_Line_Item__c. Forecast lines will be empty.');
                    pageData.forecastLines = new List<Cashflow_Line_Item__c>();
                } else {
                    // --- Step 1: Query only the parent records first. ---
                    String forecastQuery = 'SELECT ' + String.join(forecastParentFields, ',') +
                        ' FROM Cashflow_Line_Item__c WHERE Cashflow__c = :activeCashflowIdToQuery ' +
                        ' ORDER BY Week_Start_Date__c NULLS FIRST, Line_Item_Category__c';
                    DebugLogUtil.info(METHOD_NAME + ': Dynamic Forecast Lines Query (parent only): ' + forecastQuery);
                    List<Cashflow_Line_Item__c> parentLines = Database.query(forecastQuery);
                    DebugLogUtil.info(METHOD_NAME + ': ' + 'parentLines -> ' + parentLines);

                    if (!parentLines.isEmpty()) {
                        // --- Step 2: Prepare a map of generic data to build the final structure. ---
                        Map<Id, Map<String, Object>> parentDataMap = new Map<Id, Map<String, Object>>();
                        for(Cashflow_Line_Item__c parent : parentLines){
                            if(parent.Type__c == 'Financing Source') {
                                SYstem.debug('FS parent -> ' + parent);
                            }
                            Map<String, Object> genericParent = (Map<String, Object>)JSON.deserializeUntyped(JSON.serialize(parent));

                            // ** FIX: Create the wrapper map that mimics a SOQL sub-query result **
                            Map<String, Object> childQueryResultWrapper = new Map<String, Object>{
                                'totalSize' => 0,
                                'done' => true,
                                'records' => new List<SObject>()
                            };
                            // Put this wrapper into the parent map under the relationship name
                            genericParent.put(CHILD_LINE_ITEM_RELATIONSHIP_NAME, childQueryResultWrapper);

                            parentDataMap.put(parent.Id, genericParent);
                             // Create the wrapper for the disbursement relationship
                            Map<String, Object> disbursementQueryResultWrapper = new Map<String, Object>{
                                'totalSize' => 0,
                                'done' => true,
                                'records' => new List<Object>()
                            };
                            // Put this wrapper into the parent map under the new relationship name
                            genericParent.put(DISBURSEMENT_JUNCTION_RELATIONSHIP_NAME, disbursementQueryResultWrapper);

                            // END: Add this snippet
                            parentDataMap.put(parent.Id, genericParent);
                            // Create the wrapper for the disbursement relationship
                            Map<String, Object> payApplicationQueryResultWrapper = new Map<String, Object>{
                                'totalSize' => 0,
                                'done' => true,
                                'records' => new List<Object>()
                            };
                            // Put this wrapper into the parent map under the new relationship name
                            // genericParent.put(Pay_Application_JUNCTION_RELATIONSHIP_NAME, payApplicationQueryResultWrapper);
                            // // END: Add this snippet

                            // parentDataMap.put(parent.Id, genericParent);
                        }

                        // System.debug('parentDataMap -> ' + parentDataMap);

                        // --- Step 3: Query the Junction records to get the parent-to-child links. ---
                        List<Cashflow_Line_Item_Junction__c> junctions = [
                            SELECT Cashflow_Line_Item__c, Cashflow_Line_Item_Child__c
                            FROM Cashflow_Line_Item_Junction__c
                            WHERE Cashflow_Line_Item__c IN :parentDataMap.keySet()
                        ];
                        System.debug('junctions -> ' + junctions);

                        // --- Step 3a: Query the Disbursement Junction records. ---
                        List<Cashflow_Weekly_Line_Disbursement__c> disbursementJunctions = [
                            SELECT Cashflow_Line_Item__c, Disbursement__c
                            FROM Cashflow_Weekly_Line_Disbursement__c
                            WHERE Cashflow_Line_Item__c IN :parentDataMap.keySet()
                        ];

                        // List<Cashflow_Weekly_Line_Pay_Application__c> payApplicationJunctions = [
                        //     SELECT Cashflow_Line_Item__c, Pay_Application__c
                        //     FROM Cashflow_Weekly_Line_Pay_Application__c
                        //     WHERE Cashflow_Line_Item__c IN :parentDataMap.keySet()
                        // ];

                        // --- Step 4: Collect all unique child IDs and query them in a separate, simple query. ---
                        Set<Id> childIds = new Set<Id>();
                        for(Cashflow_Line_Item_Junction__c j : junctions){
                            childIds.add(j.Cashflow_Line_Item_Child__c);
                        }

                        Set<Id> disbursementIds = new Set<Id>();
                        for(Cashflow_Weekly_Line_Disbursement__c dj : disbursementJunctions){
                            disbursementIds.add(dj.Disbursement__c);
                        }
                        DebugLogUtil.info('disbursementIds -> ' + disbursementIds);

                        // Set<Id> payApplicationIds = new Set<Id>();
                        // for(Cashflow_Weekly_Line_Pay_Application__c dj : payApplicationJunctions){
                        //     payApplicationIds.add(dj.Pay_Application__c);
                        // }

                        List<String> disbursementFields = getAllAccessibleFields(DISBURSEMENT_OBJECT_API_NAME);
                        Map<Id, Disbursement__c> disbursementsMap = new Map<Id, Disbursement__c>();
                        if(!disbursementIds.isEmpty() && !disbursementFields.isEmpty()){
                            if (!disbursementFields.contains('Id')) disbursementFields.add('Id');
                            String disbursementQuery = 'SELECT ' + String.join(disbursementFields, ',') +
                                                        ' FROM Disbursement__c WHERE Id IN :disbursementIds';
                            disbursementsMap = new Map<Id, Disbursement__c>(
                                (List<Disbursement__c>)Database.query(disbursementQuery)
                            );
                        }
                        DebugLogUtil.info('disbursementsMap -> ' + disbursementsMap);

                         

                        // DebugLogUtil.info('payApplicationIds -> ' + payApplicationIds);

                        // List<String> payApplicationFields = getAllAccessibleFields(Pay_Application_OBJECT_API_NAME);
                        // Map<Id, Pay_Application__c> payApplicationsMap = new Map<Id, Pay_Application__c>();
                        // if(!payApplicationIds.isEmpty() && !payApplicationFields.isEmpty()){
                        //     if (!payApplicationFields.contains('Id')) payApplicationFields.add('Id');
                        //     String payApplicationQuery = 'SELECT ' + String.join(payApplicationFields, ',') +
                        //                                 ' FROM Pay_Application__c WHERE Id IN :payApplicationIds';
                        //     payApplicationsMap = new Map<Id, Pay_Application__c>(
                        //         (List<Pay_Application__c>)Database.query(payApplicationQuery)
                        //     );
                        // }
                        // DebugLogUtil.info('payApplicationsMap -> ' + payApplicationsMap);

                        List<String> childFields = getAllAccessibleFields(CHILD_LINE_ITEM_CHILD_OBJECT_API_NAME);
                        Map<Id, Cashflow_Line_Item_Child__c> childrenMap = new Map<Id, Cashflow_Line_Item_Child__c>();
                        if(!childIds.isEmpty() && !childFields.isEmpty()){
                            if (!childFields.contains('Id')) childFields.add('Id'); // Ensure Id is present
                            String childQuery = 'SELECT ' + String.join(childFields, ',') +
                                                ' FROM Cashflow_Line_Item_Child__c WHERE Id IN :childIds';
                            DebugLogUtil.info('childQuery -> ' + childQuery);
                            childrenMap = new Map<Id, Cashflow_Line_Item_Child__c>(
                                (List<Cashflow_Line_Item_Child__c>)Database.query(childQuery)
                            );
                        }
                        System.debug('childrenMap -> ' + childrenMap);



                        // --- Step 5: "Stitch" the queried children into the generic parent maps using the junctions. ---
                        for (Cashflow_Line_Item_Junction__c j : junctions) {
                            if (parentDataMap.containsKey(j.Cashflow_Line_Item__c) && childrenMap.containsKey(j.Cashflow_Line_Item_Child__c)) {
                                Map<String, Object> genericParent = parentDataMap.get(j.Cashflow_Line_Item__c);
                                Cashflow_Line_Item_Child__c child = childrenMap.get(j.Cashflow_Line_Item_Child__c);

                                // ** FIX: Get the wrapper map, then the list inside it **
                                Map<String, Object> childQueryResultWrapper = (Map<String, Object>)genericParent.get(CHILD_LINE_ITEM_RELATIONSHIP_NAME);
                                List<SObject> childrenList = (List<SObject>)childQueryResultWrapper.get('records');

                                // Add the child to the list
                                childrenList.add(child);

                                // Update the size in the wrapper
                                childQueryResultWrapper.put('totalSize', childrenList.size());
                            }
                        }

                        // --- Step 5a: "Stitch" the queried disbursements into the generic parent maps. ---
                        for (Cashflow_Weekly_Line_Disbursement__c dj : disbursementJunctions) {
                            if (parentDataMap.containsKey(dj.Cashflow_Line_Item__c) && disbursementsMap.containsKey(dj.Disbursement__c)) {
                                // Get the parent map to add the stitched record to
                                Map<String, Object> genericParent = parentDataMap.get(dj.Cashflow_Line_Item__c);
                                
                                // Get the specific Disbursement__c record from your map
                                Disbursement__c disbursementRecord = disbursementsMap.get(dj.Disbursement__c);

                                // Convert the junction record itself to a generic map
                                Map<String, Object> genericJunctionMap = (Map<String, Object>)JSON.deserializeUntyped(JSON.serialize(dj));

                                // Now, stitch the full Disbursement__c record INTO the junction map.
                                // The key should be the relationship name from the junction object to the disbursement object.
                                // This is typically 'Disbursement__r'.
                                genericJunctionMap.put('Disbursement__r', disbursementRecord);
                                
                                // Get the list from the parent's wrapper map
                                Map<String, Object> disbursementQueryResultWrapper = (Map<String, Object>)genericParent.get(DISBURSEMENT_JUNCTION_RELATIONSHIP_NAME);
                                List<Object> recordsList = (List<Object>)disbursementQueryResultWrapper.get('records');

                                // Add the fully formed generic junction map (which now contains the disbursement details) to the list
                                recordsList.add(genericJunctionMap);

                                // Update the size in the wrapper
                                disbursementQueryResultWrapper.put('totalSize', recordsList.size());
                            }
                        }

                        DebugLogUtil.info('parentDataMap -> ' + parentDataMap);

                          // --- Step 5a: "Stitch" the queried disbursements into the generic parent maps. ---
                        // for (Cashflow_Weekly_Line_Pay_Application__c dj : payApplicationJunctions) {
                        //     if (parentDataMap.containsKey(dj.Cashflow_Line_Item__c) && payApplicationsMap.containsKey(dj.Pay_Application__c)) {
                        //         // Get the parent map to add the stitched record to
                        //         Map<String, Object> genericParent = parentDataMap.get(dj.Cashflow_Line_Item__c);
                        //         // Get the specific Pay_Application__c record from your map
                        //         Pay_Application__c payApplicationRecord = payApplicationsMap.get(dj.Pay_Application__c);
                        //         // Convert the junction record itself to a generic map
                        //         Map<String, Object> genericJunctionMap = (Map<String, Object>)JSON.deserializeUntyped(JSON.serialize(dj));
                        //         // Now, stitch the full Pay_Application__c record INTO the junction map.
                        //         // The key should be the relationship name from the junction object to the pay Application object.
                        //         // This is typically 'Pay_Application__c'.
                        //         genericJunctionMap.put('Pay_Application', payApplicationRecord);

                        //         // Get the list from the parent's wrapper map
                        //         Map<String, Object> payApplicationQueryResultWrapper = (Map<String, Object>)genericParent.get(Pay_Application_JUNCTION_RELATIONSHIP_NAME);
                        //         List<Object> recordsList = (List<Object>)payApplicationQueryResultWrapper.get('records');
                        //         // Add the fully formed generic junction map (which now contains the pay Application details) to the list
                        //         recordsList.add(genericJunctionMap);
                        //         // Update the size in the wrapper
                        //         payApplicationQueryResultWrapper.put('totalSize', recordsList.size());
                        //     }
                        // }
                        // DebugLogUtil.info('parentDataMap -> ' + parentDataMap);

                        // --- Step 6: Re-serialize and deserialize to create the final, strongly-typed list. ---
                        String finalJson = JSON.serialize(parentDataMap.values());
                        System.debug('finalJson -> ' + finalJson);
                        DebugLogUtil.info(METHOD_NAME + ': finalJson : ' + finalJson);
                        List<Cashflow_Line_Item__c> finalForecastLines = (List<Cashflow_Line_Item__c>)JSON.deserialize(finalJson, List<Cashflow_Line_Item__c>.class);
                        System.debug('finalForecastLines -> ' + finalForecastLines);
                        // Assign the perfectly structured list to the page data.
                        // SObjectAccessDecision forecastDecision = Security.stripInaccessible(AccessType.READABLE, finalForecastLines);
                        // pageData.forecastLines = (List<Cashflow_Line_Item__c>)forecastDecision.getRecords();
                        pageData.forecastLines = finalForecastLines;
                        DebugLogUtil.info(METHOD_NAME + ': pageData.forecastLines : ' + pageData.forecastLines);
                    }
                }

                
            } else {
                DebugLogUtil.warn(METHOD_NAME + ': No active/specified Cashflow version found for Project ID: ' + projectId);
                pageData.forecastLines = new List<Cashflow_Line_Item__c>();
            }
            
            // --- 3. Get Actual Transactions (Payments, Disbursements, Adjustments) ---
            // This section remains the same unless Transaction__c structure also changed.
            List<String> transactionFields = getAllAccessibleFields('Transaction__c');
            if (transactionFields.isEmpty()) {
                DebugLogUtil.warn(METHOD_NAME + ': No accessible fields for Transaction__c, transactions will be empty.');
                pageData.transactions = new List<Transaction__c>();
            } else {
                if(!transactionFields.contains('Id')) {transactionFields.add('Id');}
                if(!transactionFields.contains('Project__c')) {transactionFields.add('Project__c');}
                // Add other essential fields as needed, e.g. 'Transaction_Date__c', 'Amount__c'
                if(!transactionFields.contains('Transaction_Date__c')){ transactionFields.add('Transaction_Date__c');}
                
                
                String transactionQuery = 'SELECT ' + String.join(transactionFields, ',') +
                    ' FROM Transaction__c WHERE Project__c = :projectId ' +
                    ' ORDER BY Transaction_Date__c';
                DebugLogUtil.info(METHOD_NAME + ': Transaction Query: ' + transactionQuery);
                
                List<SObject> tempTransactions = Database.query(transactionQuery);
                SObjectAccessDecision transactionDecision = Security.stripInaccessible(AccessType.READABLE, tempTransactions);
                pageData.transactions = (List<Transaction__c>)transactionDecision.getRecords();
                DebugLogUtil.info(METHOD_NAME + ': ' + pageData.transactions.size() + ' transactions fetched and FLS checked for Project ID: ' + projectId);
            }
            
            // --- 4. Get Disbursements ---
            List<String> disbursementFields = getAllAccessibleFields('Disbursement__c');
            if (disbursementFields.isEmpty()) {
                DebugLogUtil.warn(METHOD_NAME + ': No accessible fields for Disbursement__c, disbursements will be empty.');
                pageData.disbursements = new List<Disbursement__c>();
            } else {
                if (!disbursementFields.contains('Id')) disbursementFields.add('Id');
                if (!disbursementFields.contains('Project__c')) disbursementFields.add('Project__c');
                
                String disbursementQuery = 'SELECT ' + String.join(disbursementFields, ',') +
                    ' FROM Disbursement__c WHERE Project__c = :projectId ' +
                    ' ORDER BY CreatedDate DESC';
                DebugLogUtil.info(METHOD_NAME + ': Disbursement Query: ' + disbursementQuery);
                
                List<SObject> tempDisbursements = Database.query(disbursementQuery);
                SObjectAccessDecision disbursementDecision = Security.stripInaccessible(AccessType.READABLE, tempDisbursements);
                pageData.disbursements = (List<Disbursement__c>)disbursementDecision.getRecords();
                DebugLogUtil.info(METHOD_NAME + ': ' + pageData.disbursements.size() + ' disbursements fetched and FLS checked.');
            }
            
            // --- 5. Get Pay Applications ---
            
            List<String> payAppFields = getAllAccessibleFields('Pay_Application__c');
            if (payAppFields.isEmpty()) {
                DebugLogUtil.warn(METHOD_NAME + ': No accessible fields for Pay_Application__c, payApplications will be empty.');
                pageData.payApplications = new List<Pay_Application__c>();
            } else {
                if (!payAppFields.contains('Id')) payAppFields.add('Id');
                if (!payAppFields.contains('Project__c')) payAppFields.add('Project__c');
                
                String payAppQuery = 'SELECT ' + String.join(payAppFields, ',') +
                    ' FROM Pay_Application__c WHERE Project__c = :projectId ' +
                    ' ORDER BY CreatedDate DESC';
                DebugLogUtil.info(METHOD_NAME + ': Pay Application Query: ' + payAppQuery);
                
                List<SObject> tempPayApps = Database.query(payAppQuery);
                SObjectAccessDecision payAppDecision = Security.stripInaccessible(AccessType.READABLE, tempPayApps);
                pageData.payApplications = (List<Pay_Application__c>)payAppDecision.getRecords();
                DebugLogUtil.info(METHOD_NAME + ': ' + pageData.payApplications.size() + ' pay applications fetched and FLS checked.');
            }
            
            
        } catch (Exception e) {
            DebugLogUtil.error(METHOD_NAME + ': Error fetching cashflow data for Project ID: ' + projectId + '. Error: ' + e.getMessage() + '. Stacktrace: ' + e.getStackTraceString());
            // Rethrow to allow AuraHandledException to be caught by LWC if it's of that type
            DebugLogUtil.saveLog(Nebula.Logger.SaveMethod.QUEUEABLE);
            if (e instanceof AuraHandledException) {throw e;}
            throw new AuraHandledException('An error occurred while loading cashflow data: ' + e.getMessage());
        }
        
        DebugLogUtil.info(METHOD_NAME + ': Cashflow data retrieval complete for Project ID: ' + projectId);
        DebugLogUtil.saveLogs();
        return pageData;
    }
    
    /**
     * @description Helper method to find the Id of the currently active Cashflow version for a project.
     * @param projectId The Id of the Project__c record.
     * @return Id The Id of the active Cashflow__c record, or null if none found.
     */
    @testVisible
    private static Id getActiveCashflowId(Id projectId) {
        // ... (this method remains the same) ...
        final String METHOD_NAME = 'getActiveCashflowId';
        DebugLogUtil.entry(CLASS_NAME + '.' + METHOD_NAME, new Map<String, Object>{'projectId' => projectId});
        
        List<Cashflow__c> activeFlows = new List<Cashflow__c>();
        try {
            // Ensure FLS for Is_Active__c, Project__c, CreatedDate if not using Security.stripInaccessible here
            activeFlows = [SELECT Id,Projected_Weeks_Outstanding__c FROM Cashflow__c
                           WHERE Project__c = :projectId AND Is_Active__c = true
                           ORDER BY CreatedDate DESC LIMIT 1];
        } catch (Exception e) {
            DebugLogUtil.error(METHOD_NAME + ': Error querying active cashflow for Project ID: ' + projectId, e);
            return null;
        }
        
        Id activeFlowId = null;
        if (!activeFlows.isEmpty()) {
            activeFlowId = activeFlows[0].Id;
            DebugLogUtil.info(METHOD_NAME + ': Active Cashflow ID found: ' + activeFlowId + ' for Project ID: ' + projectId);
        } else {
            DebugLogUtil.info(METHOD_NAME + ': No active Cashflow found for Project ID: ' + projectId);
        }
        DebugLogUtil.exit(CLASS_NAME + '.' + METHOD_NAME);
        return activeFlowId;
    }
    
    /**
     * @description Wrapper class to structure the data returned to the LWC.
     */
    public class CashflowPageData {
        @AuraEnabled public Project__c project { get; set; }
        @AuraEnabled public Account account { get; set; }
        @AuraEnabled public Opportunity opp { get; set; }
        @AuraEnabled public Cashflow__c activeCashflow { get; set; }
        @AuraEnabled public List<Cashflow__c>   allCashflows  { get; set; }
        // forecastLines now contains parent Cashflow_Line_Item__c records,
        // each potentially containing a list of its children (Cashflow_Line_Item_Child__c)
        @AuraEnabled public List<Cashflow_Line_Item__c> forecastLines { get; set; }
        @AuraEnabled public List<Transaction__c> transactions { get; set; }
        @AuraEnabled public List<Disbursement__c> disbursements {get; set;}
        @AuraEnabled public List<Pay_Application__c> payApplications {get; set;}
        
        public CashflowPageData() {
            this.forecastLines = new List<Cashflow_Line_Item__c>();
            this.transactions = new List<Transaction__c>();
        }
    }
    
    
    // TODO: Implement or update your save method in a separate Apex class or here.
    // This method would be called by the LWC's handleGlobalSave.
    @AuraEnabled
    public static void saveCashflowDetails(
        List<Cashflow_Line_Item__c> parentItemsToUpsert,
    List<Id> parentItemIdsToDelete,
    List<Cashflow_Line_Item_Child__c> childItemsToUpsert,
    List<Id> childItemIdsToDelete
    ) {
        
        try {
            if (childItemIdsToDelete != null && !childItemIdsToDelete.isEmpty()) {
                System.debug('enter childItemIdsToDelete if');
                List<Cashflow_Line_Item_Child__c> childrenToDelete = new List<Cashflow_Line_Item_Child__c>();
                for(Id childId : childItemIdsToDelete){
                    System.debug('enter childItemIdsToDelete if');
                    childrenToDelete.add(new Cashflow_Line_Item_Child__c(Id=childId));
                }
                delete as User childrenToDelete;
            }
            
            if (parentItemIdsToDelete != null && !parentItemIdsToDelete.isEmpty()) {
                System.debug('enter parentItemIdsToDelete if');
                List<Cashflow_Line_Item__c> parentsToDelete = new List<Cashflow_Line_Item__c>();
                for(Id parentId : parentItemIdsToDelete){
                    parentsToDelete.add(new Cashflow_Line_Item__c(Id=parentId));
                }
                delete as User parentsToDelete;
            }
            
            if (parentItemsToUpsert != null && !parentItemsToUpsert.isEmpty()) {
                
                for (Integer i = parentItemsToUpsert.size() - 1; i >= 0; i--) {
                    String cat = parentItemsToUpsert.get(i).Line_Item_Category__c;
                    if (cat == 'Project Costs Paid That Week') {
                        parentItemsToUpsert.remove(i);
                    }
                }
                for (Cashflow_Line_Item__c cli : parentItemsToUpsert) {
                    if (cli.Line_Item_Category__c == 'Receipt of Pay App' || cli.Line_Item_Category__c == 'Pay App to be Submitted') {
                        cli.Line_Item_Category__c = 'Invoice Submission';
                    }
                }
                upsert as User parentItemsToUpsert;
            }
            if (childItemsToUpsert != null && !childItemsToUpsert.isEmpty()) {
                upsert as User childItemsToUpsert;
            }
            
        } catch (Exception e) {
            DebugLogUtil.error('saveCashflowDetails: Error saving cashflow details', e);
        }
    }
    
    
    
    
    @AuraEnabled(cacheable=false)
    public static String uploadAndParseExcel(String base64Excel) {
        // (1) Build the JSON payload
        System.debug('enter in method');
        Map<String, String> payload = new Map<String, String>{
            'fileContent' => base64Excel
        };
        String body = JSON.serialize(payload);
        System.debug('body'+body);
        // (2) Prepare the HTTP request
        HttpRequest req = new HttpRequest();
        req.setEndpoint('https://1edara8m8g.execute-api.us-east-1.amazonaws.com/parse-xls');
        req.setMethod('POST');
        req.setHeader('Content-Type', 'application/json');
        req.setBody(body);
        
        // (3) Send it
        Http http = new Http();
        HttpResponse res = http.send(req);
        
        
        // (4) Handle the response
        Integer code = res.getStatusCode();
        System.debug('code '+code);
        if (code >= 200 && code < 300) {
            System.debug('res '+res.getBody());
            // success: return the raw response body to your LWC
            return res.getBody();
        } else {
            System.debug('error '+res.getBody());
            // bubble up an error
            throw new AuraHandledException(
                'Error from parse-xls (' + code + '): ' + res.getBody()
                );
        }
    }
    

@AuraEnabled
public static Cashflow__c saveAsCashflow(
    Id originalCashflowId,
    List<Cashflow_Line_Item__c> parentItemsToUpsert,
    List<Id> parentItemIdsToDelete
) {
    try {
        // 1) Dynamic Cashflow__c field list
        List<String> cfFields = getAllAccessibleFields('Cashflow__c');
        if (!cfFields.contains('Id')) cfFields.add('Id');
        String cfSelect = String.join(cfFields, ',');

        // 2) Fetch original Cashflow__c
        String soqlOrig =
            'SELECT ' + cfSelect +
            ' FROM Cashflow__c' +
            ' WHERE Id = \'' + String.escapeSingleQuotes(originalCashflowId) + '\'';
        Cashflow__c origCf = (Cashflow__c) Database.query(soqlOrig);
        Decimal newVersion = 1;
        if (origCf.Project__c != null) {
            // grab the single highest version for that project
            List<Cashflow__c> siblings = [
                SELECT Version_Number__c
                  FROM Cashflow__c
                 WHERE Project__c = :origCf.Project__c
                   AND Version_Number__c != null
                 ORDER BY Version_Number__c DESC
                 LIMIT 1
            ];
            if (!siblings.isEmpty() && siblings[0].Version_Number__c != null) {
                newVersion = siblings[0].Version_Number__c + 1;
            }
        }
        // 3) Clone & bump version
        Cashflow__c cloneCf = origCf.clone(false, true, false, false);
        cloneCf.Version_Number__c = newVersion;
            //(origCf.Version_Number__c == null ? 1 : origCf.Version_Number__c + 1);
        cloneCf.Cashflow__c = origCf.Id;
        insert cloneCf;

        // 4) Prepare delete set, edit map, and new‐item list
        Set<Id> toDelete = (parentItemIdsToDelete == null)
            ? new Set<Id>()
            : new Set<Id>(parentItemIdsToDelete);

        Map<Id, Cashflow_Line_Item__c> edits = new Map<Id, Cashflow_Line_Item__c>();
        List<Cashflow_Line_Item__c> newItems = new List<Cashflow_Line_Item__c>();
        if (parentItemsToUpsert != null) {
            for (Cashflow_Line_Item__c e : parentItemsToUpsert) {
                if (e.Id == null) {
                    newItems.add(e);
                } else if (!toDelete.contains(e.Id)) {
                    edits.put(e.Id, e);
                }
            }
        }

        // 5) Build dynamic child‐field list for querying originals
        List<String> cliFields = getAllAccessibleFields('Cashflow_Line_Item__c');
        if (!cliFields.contains('Id'))          cliFields.add('Id');
        if (!cliFields.contains('Cashflow__c')) cliFields.add('Cashflow__c');
        String cliSelect = String.join(cliFields, ',');

        // 6) Query ALL original line‐items for the source Cashflow
        String soqlLines =
            'SELECT ' + cliSelect +
            ' FROM Cashflow_Line_Item__c' +
            ' WHERE Cashflow__c = \'' + String.escapeSingleQuotes(originalCashflowId) + '\'';
        List<Cashflow_Line_Item__c> originals =
            (List<Cashflow_Line_Item__c>) Database.query(soqlLines);

        // 7) Clone originals (skip deletions + apply edits)
        List<Cashflow_Line_Item__c> clonesToInsert = new List<Cashflow_Line_Item__c>();
        for (Cashflow_Line_Item__c orig : originals) {
            if (toDelete.contains(orig.Id)) continue;
            Cashflow_Line_Item__c c = orig.clone(false, true, false, false);
            c.Cashflow__c = cloneCf.Id;
            if (edits.containsKey(orig.Id)) {
                Cashflow_Line_Item__c userEdit = edits.get(orig.Id);
                c.Planned_Amount__c = userEdit.Planned_Amount__c;
                // …apply any other edited fields here…
            }
            clonesToInsert.add(c);
        }

        // 8) Prepare pure “new” items
        for (Cashflow_Line_Item__c e : newItems) {
            Cashflow_Line_Item__c fresh = new Cashflow_Line_Item__c();
            fresh.Cashflow__c       = cloneCf.Id;
            fresh.Planned_Amount__c = e.Planned_Amount__c;
            // …copy any other fields from e…
            clonesToInsert.add(fresh);
        }

        // 9) Bulk‐insert all clones + new items
        if (!clonesToInsert.isEmpty()) {
            insert clonesToInsert;
        }

        // 10) Build sub‐query field list for return
        // List<String> subFields = getAllAccessibleFields('Cashflow_Line_Item__c');
        // subFields.remove('Id');
        // subFields.remove('Cashflow__c');
        // String subSelect = String.join(subFields, ',');

        // 11) Return the cloned Cashflow__c with its new children
        String soqlResult =
            'SELECT ' + cfSelect +
             
            ' FROM Cashflow__c' +
            ' WHERE Id = \'' + String.escapeSingleQuotes(cloneCf.Id) + '\'';
        return (Cashflow__c) Database.query(soqlResult);

    } catch (Exception e) {
        DebugLogUtil.error('saveAsCashflow: Error cloning cashflow', e);
        throw new AuraHandledException(
            'Unable to create new Cashflow version: ' + e.getMessage()
        );
    }
}


}