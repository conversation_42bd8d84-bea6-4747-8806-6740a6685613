<template>
    <div class="slds-is-relative">
        <template if:true={isLoading}>
            <div class="slds-is-absolute slds-align_absolute-center" style="z-index:9999;">
                <lightning-spinner alternative-text="Loading..." size="medium"></lightning-spinner>
            </div>
        </template>

        

        <div class="table-outer-wrapper">
            <template if:false={isLoading}>
                <!-- This wrapper limits the visible width to 1690px (250px + 12×120px) -->
                <div class="table-scroll-container">
                    <table class="slds-table slds-table_cell-buffer slds-table_bordered slds-table_fixed-layout cashflow-table">
                        <thead>
                            <tr class="header-row-1">
                                <!-- Pinned left column is always 250px -->
                                <th class="slds-col_pinned slds-cell-fixed header-fixed-col header-top-left sticky-col" scope="col" style="width:250px;">
                                    <div class="slds-truncate header-main-title slds-grid slds-grid_vertical-align-center slds-grid_align-spread">
                                        <span>PROJECT WEEK</span>
                                        <lightning-button-icon
                                            icon-name={columnTogglerIcon}
                                            variant="bare"
                                            size="small"
                                            title={columnTogglerTitle}
                                            onclick={toggleTotalColumn}
                                            class="slds-m-right_x-small column-toggler">
                                        </lightning-button-icon>
                                    </div>
                                </th>

                                <th class={totalColumnClass} scope="col">
                                    <div class="slds-truncate">TOTAL</div>
                                </th>

                                <!-- Each week header is exactly 120px wide -->
                                <template for:each={weekColumns} for:item="weekCol" for:index="colIndex">
                                    <th 
                                      key={weekCol.id1} 
                                      scope="col" 
                                      class="slds-text-align_center week-header-cell"
                                      style="width:120px; min-width:120px;"
                                    >
                                        <div class="slds-truncate">{weekCol.label}</div>
                                    </th>
                                </template>

                                <!-- <th if:true={showTotalColumnInHeader} class="total-header" scope="col" style="width:120px; min-width:120px;">
                                    TOTALS
                                </th> -->
                            </tr>

                            <tr class="header-row-2">
                                <!-- Pinned left in second header row -->
                                <th 
                                  class="slds-col_pinned slds-cell-fixed header-fixed-col sticky-col"
                                  scope="col" 
                                  style="width:250px;"
                                >
                                    <div class="slds-truncate header-sub-title">
                                        Week Ending (Friday)
                                    </div>
                                </th>

                                <th class={totalColumnClass} scope="col"></th>

                                <!-- Each date header also 120px -->
                                <template for:each={weekColumns} for:item="weekCol" for:index="colIndex">
                                    <th 
                                      key={weekCol.id2} 
                                      scope="col" 
                                      class="slds-text-align_center date-header-cell"
                                      style="width:120px; min-width:120px;"
                                    >
                                        <div class="slds-truncate" title={weekCol.formattedDate}>
                                            {weekCol.formattedDate}
                                        </div>
                                    </th>
                                </template>

                                <!-- <th if:true={showTotalColumnInHeader} class="total-header" style="width:120px; min-width:120px;"></th> -->
                            </tr>
                        </thead>

                        <tbody>
                            <template for:each={cashFlowData} for:item="row" for:index="rowIndex">
                                <template if:true={row.isSectionHeader}>
                                    <tr key={row.id} class="section-header-row">
                                        <!-- Span pinned + all week-columns + total -->
                                        <th colspan={totalColumnCountInternalPlusTotal} scope="colgroup" class="section-title-cell">
                                            <div class="slds-grid slds-grid_align-spread slds-p-vertical_x-small slds-p-horizontal_small section-title-wrapper">
                                                <span class="section-label">{row.label}</span>
                                                <template if:true={hasFilteredCategories}>
                                                    <template if:true={row.showAddButton}>
                                                        <!-- <lightning-button-icon 
                                                          icon-name="utility:add"
                                                          alternative-text="Add line"
                                                          variant="bare"
                                                          size="small"
                                                          class="slds-m-right_x-small"
                                                          data-section-id={row.sectionId}
                                                          onclick={handleAddItem}
                                                        ></lightning-button-icon> -->
                                                    </template>
                                                </template>
                                            </div>
                                        </th>
                                    </tr>
                                </template>

                                <template if:false={row.isSectionHeader}>
                                    <tr key={row.id} class={row.rowClass}>
                                        <!-- First cell pinned -->
                                        <th class="slds-col_pinned slds-cell-fixed sticky-col" scope="row">
                                            <div 
                                              class="slds-grid slds-grid_vertical-align-center slds-grid_align-spread"
                                              
                                            >
                                                <div class="slds-truncate slds-grid slds-grid_vertical-align-center">
                                                    <template if:true={row.isExpenseCategoryChild}>
                                                        <lightning-icon 
                                                          icon-name="utility:subscription"
                                                          size="x-small"
                                                          class="slds-m-right_x-small category-icon"
                                                        ></lightning-icon>
                                                    </template>
                                                    <template if:true={row.isExpenseCategoryParent}>
                                                        <lightning-icon 
                                                          icon-name="utility:check"
                                                          size="x-small"
                                                          class="slds-m-right_x-small category-icon"
                                                        ></lightning-icon>
                                                    </template>

                                                    <template if:false={row.isNew}>
                                                        <span class={row.labelClass} title={row.label}>
                                                            {row.label}
                                                        </span>
                                                    </template>
                                                </div>

                                                <template if:true={row.showDeleteButton}>
                                                    <!-- <lightning-button-icon 
                                                      icon-name="utility:recycle_bin_empty"
                                                      alternative-text="Delete row"
                                                      title="Delete row"
                                                      variant="bare"
                                                      size="small"
                                                      class="slds-m-left_x-small"
                                                      data-row-id={row.id}
                                                      onclick={handleDeleteRowClick}
                                                      style="margin-right:17px;"
                                                    ></lightning-button-icon> -->
                                                </template>
                                            </div>
                                        </th>

                                        <td class={totalColumnClass}>
                                            <lightning-formatted-number
                                                value={row.calculatedTotal}
                                                format-style="currency"
                                                currency-code="USD"
                                                currency-display-as="symbol">
                                            </lightning-formatted-number>
                                        </td>

                                        <!-- Each data cell is 120px wide -->
                                        <template for:each={row.weeks} for:item="cell" for:index="colIndex">
                                            <td 
                                              key={cell.id} 
                                              data-label={cell.weekLabel} 
                                              class={cell.computedClass}
                                              style="width:120px; min-width:120px;"
                                            >
                                                <template if:true={cell.isDifference}>
                                                    <button 
                                                      class="slds-button slds-button_reset cell-button slds-truncate"
                                                      title="Edit Details"
                                                      onclick={handleCellClick}
                                                      data-row-index={rowIndex}
                                                      data-col-index={colIndex}
                                                    >
                                                        <lightning-formatted-number
                                                            value={cell.value}
                                                            format-style="currency"
                                                            currency-code="USD"
                                                            class="cell-value-display"
                                                        ></lightning-formatted-number>
                                                    </button>
                                                </template>
                                                <template if:false={cell.isDifference}>
                                                    <lightning-formatted-number
                                                        value={cell.value}
                                                        format-style="currency"
                                                        currency-code="USD"
                                                        class="cell-value-display"
                                                    ></lightning-formatted-number>
                                                </template>
                                            </td>
                                        </template>

                                        <!-- <td 
                                          if:true={row.showTotalCell} 
                                          class="calculated-total slds-text-align_center"
                                          style="width:120px; min-width:120px;"
                                        >
                                            <lightning-formatted-number 
                                              value={row.rowTotalValue}
                                              format-style="currency"
                                              currency-code="USD"
                                            ></lightning-formatted-number>
                                        </td> -->
                                    </tr>
                                </template>
                            </template>
                        </tbody>
                    </table>

                    <c-cashflow-edit-modal 
                      is-visible={isModalVisible} 
                      modal-title="Edit Linked (Fixed) Line Item"
                      expense-category-options={expenseCategoryOptions}
                      payment-frequency-options={paymentFrequencyOptionsInternal}
                      payment-term-options={paymentTermOptions} 
                      onsave={handleModalSave} 
                      onunlink={handleModalUnlink}
                      ondeleteitem={handleModalDelete} 
                      onclosemodal={handleCloseModal}
                      ontoasterror={handleModalToastError} 
                      object-api-name-for-form="Cashflow_Line_Item__c"
                    >
                    </c-cashflow-edit-modal>
                </div>
            </template>
            
        </div>
    </div>

  
        <c-cashflow-project-cashflow-sidebar project-data={projectData} week-columns={weekColumns} active-cashflow={activeCashflow} cashflow-data={cashFlowData} onweekcolumnschange={handleWeekColumnsChange} ></c-cashflow-project-cashflow-sidebar>
    
    

    <template if:true={isCategoryModalOpen}>
        <section role="dialog" tabindex="-1" class="slds-modal slds-fade-in-open" aria-modal="true">
            <div class="slds-modal__container slds-modal__container_small">
                <header class="slds-modal__header">
                    <button 
                      class="slds-button slds-button_icon slds-modal__close"
                      title="Close"
                      onclick={closeCategoryModal}
                    >
                        <lightning-icon icon-name="utility:close" alternative-text="Close"></lightning-icon>
                        <span class="slds-assistive-text">Close</span>
                    </button>
                    <h2 class="slds-text-heading_medium">Select Expense Category</h2>
                </header>

                <div class="slds-modal__content slds-p-around_medium">
                    <lightning-radio-group 
                      name="categoryPicker" 
                      options={filteredExpenseCategoryOptions}
                      value={selectedCategory} 
                      type="radio" 
                      onchange={handleRadioChange}
                    >
                    </lightning-radio-group>
                </div>

                <footer class="slds-modal__footer slds-grid slds-grid_align-end">
                    <lightning-button label="Cancel" onclick={closeCategoryModal}></lightning-button>
                    <button 
                      class="custom-green-button slds-button slds-m-left_xx-small"
                      onclick={confirmCategorySelection}
                      disabled={isAddDisabled}
                    >
                        Add
                    </button>
                </footer>
            </div>
        </section>
        <div class="slds-backdrop slds-backdrop_open"></div>
    </template>

    

</template>